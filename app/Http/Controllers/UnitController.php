<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreUnitRequest;
use App\Http\Requests\UpdateUnitRequest;
use App\Http\Traits\HandlesApiResponses;
use App\Models\Unit;
use App\Services\UnitService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UnitController extends Controller
{
    use HandlesApiResponses;

    public function __construct(
        private readonly UnitService $service
    ) {}

    /**
     * Transform unit data to use direct assessment format
     */
    private function transformUnitData(Unit $unit): array
    {
        $unitData = $unit->toArray();
        $unitData['assessments'] = $unit->getDirectAssessments()->toArray();
        return $unitData;
    }

    /**
     * Transform pagination data with direct assessments
     */
    private function transformPaginationData($units): array
    {
        $paginationData = $units->toArray();
        $paginationData['data'] = collect($units->items())->map(
            fn(Unit $unit) => $this->transformUnitData($unit)
        )->toArray();
        return $paginationData;
    }

    /**
     * Display a listing of units with optional course filtering
     */
    public function index(Request $request): JsonResponse
    {
        $query = Unit::with(['course', 'assessments'])
            ->orderBy('course_id')
            ->orderBy('unit_order');

        // Filter by course if provided
        if ($request->has('course_id')) {
            $query->where('course_id', $request->get('course_id'));
        }

        // Filter by skill type if provided
        if ($request->has('skill_type')) {
            $query->where('skill_type', $request->get('skill_type'));
        }

        // Filter by difficulty if provided
        if ($request->has('difficulty')) {
            $query->where('difficulty', $request->get('difficulty'));
        }

        $perPage = min($request->get('per_page', 15), 100);
        $units = $query->paginate($perPage);

        return $this->successResponse(
            'Units retrieved successfully',
            $this->transformPaginationData($units)
        );
    }

    /**
     * Display the specified unit
     */
    public function show(Unit $unit): JsonResponse
    {
        $unit->load(['course', 'assessments']);

        return $this->successResponse(
            'Unit retrieved successfully',
            $this->transformUnitData($unit)
        );
    }

    /**
     * Store a newly created unit
     */
    public function store(StoreUnitRequest $request): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($request) {
                $unit = $this->service->create($request->validated());
                $unit->load(['course', 'assessments']);

                return $this->successResponse(
                    'Unit created successfully',
                    $this->transformUnitData($unit),
                    201
                );
            },
            'create unit',
            "course_id: {$request->input('course_id')}"
        );
    }

    /**
     * Update the specified unit
     */
    public function update(UpdateUnitRequest $request, Unit $unit): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($unit, $request) {
                $updatedUnit = $this->service->update($unit, $request->validated());
                $updatedUnit->load(['course', 'assessments']);

                return $this->successResponse(
                    'Unit updated successfully',
                    $this->transformUnitData($updatedUnit)
                );
            },
            'update unit',
            "unit_id: {$unit->id}"
        );
    }

    /**
     * Remove the specified unit
     */
    public function destroy(Unit $unit): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($unit) {
                $deleted = $this->service->delete($unit);

                if (!$deleted) {
                    return $this->errorResponse('Failed to delete unit', null, 500);
                }

                return $this->successResponse('Unit deleted successfully');
            },
            'delete unit',
            "unit_id: {$unit->id}"
        );
    }

    /**
     * Duplicate an existing unit
     */
    public function duplicate(Request $request, Unit $unit): JsonResponse
    {
        $request->validate([
            'target_order' => 'nullable|integer|min:1',
        ]);

        return $this->executeWithErrorHandling(
            function() use ($request, $unit) {
                $duplicatedUnit = $this->service->duplicate($unit, $request->input('target_order'));
                $duplicatedUnit->load(['course', 'assessments']);

                return $this->successResponse(
                    'Unit duplicated successfully',
                    $this->transformUnitData($duplicatedUnit),
                    201
                );
            },
            'duplicate unit',
            "unit_id: {$unit->id}"
        );
    }

    /**
     * Move unit to different course
     */
    public function moveToCourse(Request $request, Unit $unit): JsonResponse
    {
        $request->validate([
            'target_course_id' => 'required|integer|exists:courses,id',
            'target_order' => 'nullable|integer|min:1',
        ]);

        return $this->executeWithErrorHandling(
            function() use ($request, $unit) {
                $movedUnit = $this->service->moveToCourse(
                    $unit,
                    $request->input('target_course_id'),
                    $request->input('target_order')
                );
                $movedUnit->load(['course', 'assessments']);

                return $this->successResponse(
                    'Unit moved to course successfully',
                    $this->transformUnitData($movedUnit)
                );
            },
            'move unit to course',
            "unit_id: {$unit->id}, target_course_id: {$request->input('target_course_id')}"
        );
    }

    /**
     * Reorder units within a course
     */
    public function reorder(Request $request, Unit $unit): JsonResponse
    {
        $request->validate([
            'new_order' => 'required|integer|min:1',
            'course_id' => 'nullable|integer|exists:courses,id',
        ]);

        return $this->executeWithErrorHandling(
            function() use ($request, $unit) {
                $courseId = $request->input('course_id', $unit->course_id);
                $this->service->reorderUnits($unit, $request->input('new_order'), $courseId);

                // Update the unit's order
                $unit->update(['unit_order' => $request->input('new_order')]);
                $unit->load(['course', 'assessments']);

                return $this->successResponse(
                    'Unit reordered successfully',
                    $this->transformUnitData($unit)
                );
            },
            'reorder unit',
            "unit_id: {$unit->id}, new_order: {$request->input('new_order')}"
        );
    }

    /**
     * Get units by course
     */
    public function getByCourse(Request $request, int $courseId): JsonResponse
    {
        $perPage = min($request->get('per_page', 50), 100);

        $query = Unit::with(['assessments'])
            ->where('course_id', $courseId)
            ->orderBy('unit_order');

        // Filter by skill type if provided
        if ($request->has('skill_type')) {
            $query->where('skill_type', $request->get('skill_type'));
        }

        // Filter by difficulty if provided
        if ($request->has('difficulty')) {
            $query->where('difficulty', $request->get('difficulty'));
        }

        $units = $query->paginate($perPage);

        return $this->successResponse(
            'Course units retrieved successfully',
            $this->transformPaginationData($units)
        );
    }

    public function getByCoursePublic(Request $request, int $courseId): JsonResponse
    {
        $perPage = min($request->get('per_page', 50), 100);

        $query = Unit::with(['assessments'])
            ->where('course_id', $courseId)
            ->whereHas('assessments')
            ->orderBy('unit_order');

        $units = $query->paginate($perPage);

        return $this->successResponse(
            'Course units retrieved successfully',
            $units
        );
    }

    /**
     * Clear all assessments from a unit (completely delete assessments)
     */
    public function clearAssessments(int $id): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($id) {
                $cleared = $this->service->clearUnitAssessments($id);

                return $this->successResponse(
                    "Deleted {$cleared} assessments from unit",
                    ['deleted_count' => $cleared]
                );
            },
            'clear unit assessments',
            "unit_id: {$id}"
        );
    }
}
