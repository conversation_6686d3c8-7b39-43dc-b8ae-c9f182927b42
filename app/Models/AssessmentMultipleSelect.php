<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class AssessmentMultipleSelect extends Model
{
    use HasFactory;

    protected $fillable = [
        'question',
        'answer_list',
        'correct_answer_indexes',
        'explanations',
    ];

    protected $casts = [
        'answer_list' => 'array',
        'correct_answer_indexes' => 'array',
        'explanations' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function assessment(): MorphOne
    {
        return $this->morphOne(Assessment::class, 'itemable');
    }
}
