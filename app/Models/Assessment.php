<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Assessment extends Model
{
    use HasFactory;

    protected $fillable = [
        'itemable_id',
        'itemable_type',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function itemable(): MorphTo
    {
        return $this->morphTo();
    }

    public function units(): BelongsToMany
    {
        return $this->belongsToMany(Unit::class, 'unit_assessments')
            ->withPivot('assessment_order')
            ->orderByPivot('assessment_order');
    }

    /**
     * Get context from assessment based on its type
     */
    public function getContext(): string
    {
        return $this->itemable?->context ?? '';
    }

    /**
     * Get question from assessment based on its type
     */
    public function getQuestion(): string
    {
        return $this->itemable?->question ?? '';
    }

    /**
     * Get fill position from assessment (for AI gap fill assessments)
     */
    public function getFillPosition(): array
    {
        return $this->itemable?->fill_position ?? [];
    }
}
