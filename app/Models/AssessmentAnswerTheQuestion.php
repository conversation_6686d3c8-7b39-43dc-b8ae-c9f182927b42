<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class AssessmentAnswerTheQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'question',
        'correct_answers',
        'explanation',
    ];

    protected $casts = [
        'correct_answers' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function assessment(): MorphOne
    {
        return $this->morphOne(Assessment::class, 'itemable');
    }
}
