<?php

namespace App\Models;

use App\Enums\Difficulty;
use App\Enums\SkillType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Unit extends Model
{
    use HasFactory;

    protected $fillable = [
        'course_id',
        'title',
        'description',
        'skill_type',
        'difficulty',
        'unit_type',
        'unit_order',
    ];

    protected $casts = [
        'skill_type' => SkillType::class,
        'difficulty' => Difficulty::class,
        'unit_type' => \App\Enums\UnitType::class,
        'unit_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    public function assessments(): BelongsToMany
    {
        return $this->belongsToMany(Assessment::class, 'unit_assessments')
            ->withPivot('assessment_order')
            ->orderByPivot('assessment_order')
            ->with('itemable');
    }

    /**
     * Get assessments with direct itemable content
     */
    public function getDirectAssessments()
    {
        return $this->assessments->map(function ($assessment) {
            if ($assessment->itemable) {
                // Add assessment_order to the itemable object
                $itemable = $assessment->itemable;
                $itemable->assessment_order = $assessment->pivot->assessment_order;
                return $itemable;
            }
            return $assessment;
        });
    }
}
